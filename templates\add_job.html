{% extends 'base.html' %}

{% block title %}Add Job - HR Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>Add New Job
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_job') }}">
                    <div class="mb-3">
                        <label for="title" class="form-label">Job Title</label>
                        <input type="text" class="form-control" id="title" name="title" value="{{ title or '' }}" required>
                        <div class="form-text">Enter a descriptive title for the job position.</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Job Description</label>
                        <textarea class="form-control" id="description" name="description" rows="6" required>{{ description or '' }}</textarea>
                        <div class="form-text">
                            Provide a detailed description of the job, including required skills, responsibilities, and experience level.
                        </div>
                    </div>

                    <!-- Job Details Section -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="platform" class="form-label">Platform</label>
                            <select class="form-select" id="platform" name="platform">
                                <option value="">Select Platform</option>
                                <option value="LinkedIn">LinkedIn</option>
                                <option value="Instagram">Instagram</option>
                                <option value="Jobcenter.de">Jobcenter.de</option>
                                <option value="Email Campaign">Email Campaign</option>
                                <option value="Unsolicited Pool">Unsolicited Pool</option>
                                <option value="Internal">Internal</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="Active">Active</option>
                                <option value="Open Pool">Open Pool</option>
                                <option value="Ending Soon">Ending Soon</option>
                                <option value="Expired">Expired</option>
                                <option value="On Hold">On Hold</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="job_url" class="form-label">Job Posting URL</label>
                        <input type="url" class="form-control" id="job_url" name="job_url" placeholder="https://www.linkedin.com/jobs/view/...">
                        <div class="form-text">
                            Enter the URL of the job posting (e.g., LinkedIn, company website, etc.). This will make the platform clickable in the job board.
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                    </div>

                    <!-- Responsible People Section -->
                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-users me-2"></i>Responsible People
                        </h5>

                        <!-- Main Responsible Person -->
                        <div class="card mb-3">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-user-tie me-2"></i>Main Responsible Person
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="main_name" class="form-label">Name</label>
                                        <input type="text" class="form-control" id="main_name" name="main_name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="main_email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="main_email" name="main_email" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Responsible People -->
                        <div class="card">
                            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2"></i>Additional Responsible People
                                </h6>
                                <button type="button" class="btn btn-sm btn-light" id="addPersonBtn">
                                    <i class="fas fa-plus me-1"></i>Add Person
                                </button>
                            </div>
                            <div class="card-body" id="additionalPeople">
                                <p class="text-muted mb-0">Click "Add Person" to add additional responsible people.</p>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('jobs') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Jobs
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Save Job
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let personCounter = 0;
    const addPersonBtn = document.getElementById('addPersonBtn');
    const additionalPeople = document.getElementById('additionalPeople');

    addPersonBtn.addEventListener('click', function() {
        personCounter++;

        // Remove the placeholder text if it exists
        const placeholder = additionalPeople.querySelector('p.text-muted');
        if (placeholder) {
            placeholder.remove();
        }

        const personDiv = document.createElement('div');
        personDiv.className = 'person-entry mb-3 p-3 border rounded';
        personDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Person ${personCounter}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-person">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Name</label>
                    <input type="text" class="form-control" name="additional_names[]" required>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" name="additional_emails[]" required>
                </div>
            </div>
        `;

        additionalPeople.appendChild(personDiv);

        // Add remove functionality
        personDiv.querySelector('.remove-person').addEventListener('click', function() {
            personDiv.remove();

            // If no more people, show placeholder
            if (additionalPeople.children.length === 0) {
                additionalPeople.innerHTML = '<p class="text-muted mb-0">Click "Add Person" to add additional responsible people.</p>';
            }
        });
    });
});
</script>
{% endblock %}
