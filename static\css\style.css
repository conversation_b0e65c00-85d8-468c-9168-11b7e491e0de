/* Custom styles for BAUCH HR Management System */

:root {
    --bauch-red: #e63946;
    --bauch-dark: #1d1d1d;
    --bauch-darker: #121212;
    --bauch-light: #f8f9fa;
    --bauch-gray: #6c757d;
    --bauch-light-gray: #e9ecef;
}

/* General styles */
body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* Card hover effects */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Card headers */
.card-header {
    border-bottom: none;
    padding: 1.25rem 1.5rem;
}

.card-header.bg-primary {
    background-color: var(--bauch-dark) !important;
}

/* Custom navbar styling */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 0.8rem 1rem;
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
    color: var(--bauch-dark) !important;
}

.navbar-brand-logo {
    height: 60px;
    width: auto;
    max-width: 250px;
    object-fit: contain;
    transition: transform 0.2s ease;
}

.navbar-brand-logo:hover {
    transform: scale(1.05);
}

.bg-primary {
    background-color: white !important;
    border-bottom: 1px solid #dee2e6;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--bauch-dark);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--bauch-red);
    background-color: rgba(0, 0, 0, 0.05);
}

/* Navbar logos styling */
.navbar-logos {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-logo {
    height: 50px;
    width: auto;
    max-width: 80px;
    object-fit: contain;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.navbar-logo:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

/* Dashboard cards */
.card .display-4 {
    font-weight: bold;
    color: var(--bauch-red);
}

/* Form styling */
.form-control, .form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--bauch-red);
    box-shadow: 0 0 0 0.25rem rgba(230, 57, 70, 0.25);
}

textarea.form-control {
    min-height: 150px;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Button enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--bauch-red);
    border-color: var(--bauch-red);
}

.btn-primary:hover {
    background-color: #d32836;
    border-color: #c42231;
    transform: translateY(-2px);
}

.btn-success {
    background-color: #2a9d8f;
    border-color: #2a9d8f;
}

.btn-info {
    background-color: #457b9d;
    border-color: #457b9d;
    color: white;
}

.btn-danger {
    background-color: var(--bauch-red);
    border-color: var(--bauch-red);
}

.btn-outline-primary {
    border-color: var(--bauch-red);
    color: var(--bauch-red);
}

.btn-outline-primary:hover {
    background-color: var(--bauch-red);
    border-color: var(--bauch-red);
    color: white;
    transform: translateY(-2px);
}

/* Table styling */
.table {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.table-dark th {
    background-color: var(--bauch-dark);
    color: white;
    font-weight: 500;
    border-bottom: none;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(230, 57, 70, 0.05);
}

/* Progress bar styling */
.progress {
    height: 0.8rem;
    border-radius: 20px;
    background-color: #e9ecef;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.progress-bar {
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.7rem;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.progress-bar.bg-success {
    background-color: #2a9d8f !important;
}

.progress-bar.bg-warning {
    background-color: #e9c46a !important;
}

.progress-bar.bg-danger {
    background-color: var(--bauch-red) !important;
}

/* Alert styling */
.alert {
    border-radius: 10px;
    border-left-width: 4px;
    padding: 1rem 1.5rem;
}

.alert-primary {
    border-left-color: var(--bauch-red);
}

/* Footer styling */
footer {
    border-top: 1px solid #dee2e6;
    margin-top: 3rem;
    padding: 2rem 0;
    color: #6c757d;
    background-color: #f8f9fa;
}

/* Modal styling */
.modal-content {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: none;
}

.modal-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    background-color: var(--bauch-dark);
    color: white;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

/* Page headers */
h1 {
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--bauch-dark);
}

h1 i {
    color: var(--bauch-red);
}

/* Card titles */
.card-title {
    font-weight: 600;
    margin-bottom: 0;
}

/* Dropdown menus */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1.25rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(230, 57, 70, 0.05);
}

.dropdown-item.text-danger {
    color: var(--bauch-red) !important;
}

.dropdown-item.text-danger:hover {
    background-color: rgba(230, 57, 70, 0.1);
}

/* Dark mode support */
[data-bs-theme="dark"] {
    --bs-body-bg: var(--bauch-darker);
    --bs-body-color: #e9ecef;
    --bs-border-color: #2c2c2c;
}

[data-bs-theme="dark"] .card {
    background-color: var(--bauch-dark);
    border-color: #2c2c2c;
}

[data-bs-theme="dark"] .table {
    color: #e9ecef;
}

[data-bs-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background-color: #2c2c2c;
    border-color: #3c3c3c;
    color: #e9ecef;
}

[data-bs-theme="dark"] footer {
    background-color: var(--bauch-dark);
    border-color: #2c2c2c;
}

/* Login page styling */
.login-logo {
    height: 80px;
    width: auto;
    max-width: 300px;
    object-fit: contain;
}

.card {
    border-radius: 15px;
}

.form-control-lg {
    border-radius: 10px;
    padding: 0.75rem 1rem;
}

.btn-lg {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.1rem;
    }

    h1 {
        font-size: 1.8rem;
    }

    .table {
        font-size: 0.9rem;
    }

    .btn {
        padding: 0.4rem 1rem;
    }

    /* Hide logos on mobile to save space */
    .navbar-logos {
        display: none;
    }

    .login-logo {
        height: 60px;
    }
}